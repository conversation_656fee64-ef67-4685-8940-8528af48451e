const fs = require('fs');
const path = require('path');
const { Sequelize } = require('sequelize')
// Construct the path to the SSL certificate
const sslCertPath = path.join(__dirname, '..', 'DigiCertGlobalRootCA.crt.pem');
//ffdsad
// const connection = new Sequelize(
// //Azure Connection
//  "postgres",
//  "sokoon",
//  "InnovationCafe@48",

//   {
//    host: 'pgsql3-sokoon-stg-qc-001.postgres.database.azure.com',
//    dialect: "postgres",
//    port: '5432',
  
//     define: {
//       timestamps: false, //turnoff timestapm
//     },
//     dialectOptions: {
//       ssl: {
//         ca: fs.readFileSync(sslCertPath), // Path to the SSL public certificate
//         // Other SSL options can be added here if needed
//       }
//     },
//     pool: {
//       max: 5,
//       min: 1,
//       idle: 10000,
//     },
//   }
// );

// Testing Azure (ra)
// const connection = new Sequelize(
//   "postgres",
//   "pgadmin",
//   "DejU.gaRNbhzjP",
//  {
//    host: 'new-pg-db.postgres.database.azure.com',
//    port: "5432",
//    dialect: "postgres",
  
//    define: {
//      timestamps: false, //turnoff timestapm
//    },
//    dialectOptions: {
// 	      ssl: {
// 	        ca: fs.readFileSync('certificate2.pem'), // Path to the SSL public certificate
// 	        // Other SSL options can be added here if needed
// 	      }
// 	    },
//    pool: {
//      max: 3,
//      min: 1,
//      idle: 10000,
//    },
//  }
// );


// const connection = new Sequelize(
// 	"postgres",
// 	"sokoon",
// 	"InnovationCafe@48",
//    {
// 	 host: 'pgsql3-sokoon-stg-qc-001.postgres.database.azure.com',
// 	 port: "5432",
// 	 dialect: "postgres",
	
// 	 define: {
// 	   timestamps: false, //turnoff timestapm
// 	 },
// 	 dialectOptions: {
// 			ssl: {
// 			  ca: fs.readFileSync('DigiCertGlobalRootCA.crt.pem'), // Path to the SSL public certificate
// 			  // Other SSL options can be added here if needed
// 			}
// 		  },
// 	 pool: {
// 	   max: 3,
// 	   min: 1,
// 	   idle: 10000,
// 	 },
//    }
//   );
const connection = new Sequelize(
	"sokoon",
	"sokoon",
	"InnovationCafe@48",
   {
	 host: 'pgsql3-sokoon-prod-qc-001.postgres.database.azure.com',
	 port: "5432",
	 dialect: "postgres",
	
	 define: {
	   timestamps: false, //turnoff timestapm
	 },
	//  dialectOptions: {
	// 		ssl: {
	// 		  ca: fs.readFileSync('DigiCertGlobalRootCA.crt.pem'), // Path to the SSL public certificate
	// 		  // Other SSL options can be added here if needed
	// 		}
	// 	  },
	 pool: {
	   max: 3,
	   min: 1,
	   idle: 10000,
	 },
   }
  );
// const connection = new Sequelize('postgres://pgadmin:<EMAIL>:5432/postgres')

module.exports.connection = connection;
