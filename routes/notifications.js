const express = require("express");
const router = express.Router();
const models = require("../models/models");
const sequelize = require("sequelize");
const Op = sequelize.Op;
const jwt = require("jsonwebtoken");
var admin = require("firebase-admin");
const axios = require("axios");
const {
    accessTokenSecret,
} = require("../config");

var serviceAccount = require("../sokoon-firebase-adminsdk.json");

// admin.initializeApp({
// 	credential: admin.credential.cert(serviceAccount)
// 	});

module.exports = function(socketIO) {
//Get Single Notification
router.get("/Get_SingleNotification/:nt_id", (req, res, next) => {
    const { nt_id } = req.params;

    models.notifications
        .findAll({
            where: {
                id: nt_id,
            }
        })

        .then((data) => {
            if (data?.length != 0) {
                console.log("Notification Get Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "Notification Get Successfully",
                });
            } else {
                console.log("No Notification Found");
                res.json({
                    successful: false,
                    message: "No Notification Found",
                });
            }
        })

        .catch(function (err) {
            console.log("Failed To Get Notification: ", err);
            res.json({
                successful: false,
                message: "Failed To Get Notification: " + err,
            });
        });
});

//Save user token
router.post('/Save_Token', async (req, res, next) => {
	const { token } = req.body?.data
	if(!token) return res.json({
		successful: false,
		message: 'Token is required'
	})
	await models.notificationToken
		.create({ token })
	res.json({
		successful: true
	});
})

//Get All Notifications
router.get("/Get_AllNotifications", (req, res, next) => {
    models.notifications
        .findAll({
            order: [["created_at", "DESC"]],
        })
        .then((data) => {
            if (data?.length > 0) {
                console.log("Get All Notifications Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "Get All Notifications Successfully",
                });
            } else {
                console.log("No Notifications Found");
                res.json({
                    successful: false,
                    message: "No Notifications Found",
                });
            }
        })
        .catch(function (err) {
            console.log("Failed To Get All Notifications: ", err);
            res.json({
                successful: false,
                message: "Failed To Get All Notifications: " + err,
            });
        });
});

// Create Notification
router.post("/Create_Notification", async (req, res, next) => {
	if (!admin.apps.length) {
		admin.initializeApp({
			credential: admin.credential.cert(serviceAccount)
		  });	
	}
    const { idWord, typenoti, title, body, messageAr, messageEn, messageFr } = req.body.data;

    const values = {
        idWord: idWord,
        title: title,
        body: body,
        typenoti: typenoti,
        messageEn: messageEn,
        messageAr: messageAr,
        messageFr: messageFr,
        created_at: new Date().toISOString(),
    };

    try {
        // Check if the notification already exists
        // const existingNotification = await models.notifications.findOne({
        //     where: { title: title }
        // });
		const last = await models.notifications.findOne({ order: [["id", "DESC"]] });

		values.id = last ? ((last.get('id') + 1) || 1) : 1
        // if (existingNotification) {
        //     console.log("Same Notification already exists");
        //     return res.json({
        //         successful: false,
        //         message: "Same Notification already exists"
        //     });
        // }
        // Create the notification
        const newNotification = await models.notifications.create(values);

		// Send notification to all users
		// const tokenData = await models.notificationToken.findAll()
		// const token = tokenData.map(item => item.token)
		// await admin.messaging().sendEachForMulticast({ tokens: token, notification: { title, body } })
		
		// await admin.messaging().sendToTopic("allDevices", {
		// 	notification: {
		// 		title,
		// 		body,
		// 		sound: "default",
		// 	},
		// })

        // Emit socket event to notify clients
        // socketIO.emit("new_notification", newNotification);
		axios.post(prcoess.env.FIREBASE_FUNCTIONS_URL +'/send', {
			key: prcoess.env.FIREBASE_FUNCTIONS_KEY,
			title,
			body,
		}).then(() => null)

        console.log("Notification Created Successfully using socket", newNotification);
        // const accessToken = jwt.sign({
        //     successful: true,
        //     message: "Notification Created Successfully",
        //     data: newNotification
        // }, accessTokenSecret);

        res.json({
            successful: true,
            message: "Notification Created Successfully",
            data: newNotification
        });

    } catch (err) {
        console.log("Failed to Create New Notification: ", err);
        res.json({
            successful: false,
            message: "Failed to Create New Notification: " + err
        });
    }
});

//Update Notification Detail
router.post("/Update_NotificationDetail", async (req, res, next) => {
    console.log("Update Notification Detail API Calling:", req.body.data);
    values = [
        {
            id: req.body.data.id,
             idWord: req.body.data.idWord,
            title: req.body.data.title,
            body: req.body.data.body,
            typenoti: req.body.data.typenoti,
            messageEn: req.body.data.messageEn,
	    messageAr: req.body.data.messageAr,
            messageFr: req.body.data.messageFr,
            updated_at: new Date().toISOString(),
        },
    ];
    await models.notifications
        .update(
            {
                idWord: values[0].idWord,
            title: values[0].title,
            body: values[0].body,
            typenoti: values[0].typenoti,
            messageEn: values[0].messageEn,
	    messageAr: values[0].messageAr,
            messageFr: values[0].messageFr,
            updated_at: values[0].updated_at,
            },
            {
                where: {
                    id: values[0].id,
                },
                returning: true,
                plain: true,
                exclude: ["created_at", "updated_at"],
            }
        )
        .then((data) => {
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "Notification Detail Updated Successfully",
                    data: data[1].dataValues,
                },
                accessTokenSecret
            );
            console.log("Response Data: ", data[1].dataValues);
            res.json({
                successful: true,
                message: "Successful",
                data: data[1].dataValues,
                accessToken,
            });
        })
        .catch(function (err) {
            console.log(err);
            res.json({
                message: "Failed" + err,
                successful: false,
            });
        });
});




//Delete Single Notification
router.get("/Delete_SingleNotification/:id", (req, res, next) => {
    const { id } = req.params;
  

	
    models.notifications
      .destroy({
        where: {
          id: id,
        },
      })
      .then((data) => {
        if (data?.length > 0) {
          console.log("Notification Deleted Successfully.");
          res.json({
            data: data,
            successful: true,
            message: "Notification Deleted Successfully.",
          });
        } else {
          console.log("No Notification Found");
          res.json({
            successful: false,
            message: "No Notification Found",
          });
        }
      })
      .catch(function (err) {
        console.log("Failed To Delete Notification: ", err);
        res.json({
          successful: false,
          message: "Failed To Delete Notification: " + err,
        });
      });
  });


  return router;
};
