const { BlobServiceClient, StorageSharedKeyCredential } = require('@azure/storage-blob');
const accountName ='stmsdfsokoonprodqc';
const accountKey ='****************************************************************************************';
// const accountName ='stmsdfsokoondevqc';
// const accountKey ='****************************************************************************************';
// Create a storage credentials object with your account name and account key
const sharedKeyCredential = new StorageSharedKeyCredential(accountName, accountKey);
// Replace with your Azure Storage connection string and container name
const connectionString = 'DefaultEndpointsProtocol=https;AccountName=stmsdfsokoonstgqc;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net';

if (!connectionString) {
    console.error("Azure Storage connection string is missing.");
    process.exit(1);
}

const containerName = 'sokoon';
// Create a BlobServiceClient object using the storage credentials
const blobServiceClient = new BlobServiceClient(`https://${accountName}.blob.core.windows.net`, sharedKeyCredential);

//const blobServiceClient = BlobServiceClient.fromConnectionString(connectionString);
const containerClient = blobServiceClient.getContainerClient(containerName);
const containers = blobServiceClient.listContainers();
module.exports = { containerClient };
